# Direct Microservice API Base URL Fix

## Problem Identified

The seller app was experiencing API base URL issues where services were configured with **incorrect base URLs that included service-specific paths**, causing double path issues:

### Root Cause
1. **Service URLs included service-specific paths** in the base URL (e.g., `http://localhost:3001/api/v1/sellers`)
2. **Double path construction** when making requests (e.g., `/sellers/sellers/login`)
3. **Missing environment variables** for direct microservice access
4. **Inconsistent URL configuration** across different parts of the application

### Example of the Problem
- **Before:** Base URL: `http://localhost:3001/api/v1/sellers` + Request path: `/sellers/login`
- **Result:** `http://localhost:3001/api/v1/sellers/sellers/login` ❌
- **After:** Base URL: `http://localhost:3001/api/v1` + Request path: `/sellers/login`
- **Result:** `http://localhost:3001/api/v1/sellers/login` ✅

## Solution Implemented

### 1. Fixed Environment Configuration
**File Modified:** `libs/shared-utils/src/lib/environment/environment.config.ts`

**Changes Made:**
- **Before:** Service URLs included service-specific paths (e.g., `/sellers`, `/admin`)
- **After:** Service URLs point to microservice base API endpoints only
- **Impact:** Eliminates double path issues and ensures correct URL construction

**Development URLs:**
```typescript
services: {
  sellerService: 'http://localhost:3001/api/v1',      // ✅ Fixed
  adminService: 'http://localhost:3002/api/v1',       // ✅ Fixed
  analyticsService: 'http://localhost:3003/api/v1',   // ✅ Fixed
  cropService: 'http://localhost:3004/api/v1',        // ✅ Fixed
  farmService: 'http://localhost:3005/api/v1',        // ✅ Fixed
  // ... other services
}
```

### 2. Updated Environment Variables
**Files Modified:**
- `apps/seller-app/.env`
- `apps/seller-app/vite.config.ts`
- `apps/seller-admin-app/vite.config.ts`

**Changes Made:**
- **Added:** Direct microservice environment variables
- **Configured:** Proper fallback URLs for development
- **Impact:** Enables direct microservice communication without API Gateway

**Environment Variables Added:**
```bash
REACT_APP_SELLER_SERVICE_URL=http://localhost:3001/api/v1
REACT_APP_ADMIN_SERVICE_URL=http://localhost:3002/api/v1
REACT_APP_ANALYTICS_SERVICE_URL=http://localhost:3003/api/v1
REACT_APP_CROP_SERVICE_URL=http://localhost:3004/api/v1
REACT_APP_FARM_SERVICE_URL=http://localhost:3005/api/v1
REACT_APP_FINANCIAL_SERVICE_URL=http://localhost:3006/api/v1
REACT_APP_INVENTORY_SERVICE_URL=http://localhost:3007/api/v1
REACT_APP_NOTIFICATION_SERVICE_URL=http://localhost:3008/api/v1
REACT_APP_ORDER_SERVICE_URL=http://localhost:3009/api/v1
REACT_APP_PLOT_SERVICE_URL=http://localhost:3010/api/v1
```

### 3. Updated Axios Instance Configuration
**File Modified:** `libs/shared-utils/src/lib/axios/instance.ts`

**Changes Made:**
- **Updated:** Comments to reflect direct microservice access
- **Clarified:** Base URL structure and path construction
- **Impact:** Better documentation of how services connect to microservices

### 4. Enhanced Debug Component
**File Modified:** `apps/seller-app/src/app/components/ApiDebugInfo.tsx`

**Changes Made:**
- **Updated:** Expected base URLs for each service
- **Enhanced:** Display to show current vs expected URLs
- **Added:** Better validation for direct microservice configuration
- **Impact:** Easier debugging and verification of API configuration

## Microservice Port Mapping

| Service | Port | Base URL | Purpose |
|---------|------|----------|---------|
| Seller Service | 3001 | `http://localhost:3001/api/v1` | Authentication, profiles, seller management |
| Admin Service | 3002 | `http://localhost:3002/api/v1` | Admin operations, user management |
| Analytics Service | 3003 | `http://localhost:3003/api/v1` | Analytics, reporting, insights |
| Crop Service | 3004 | `http://localhost:3004/api/v1` | Crop management, catalog |
| Farm Service | 3005 | `http://localhost:3005/api/v1` | Farm management, locations |
| Financial Service | 3006 | `http://localhost:3006/api/v1` | Payments, transactions |
| Inventory Service | 3007 | `http://localhost:3007/api/v1` | Stock management |
| Notification Service | 3008 | `http://localhost:3008/api/v1` | Notifications, alerts |
| Order Service | 3009 | `http://localhost:3009/api/v1` | Order processing |
| Plot Service | 3010 | `http://localhost:3010/api/v1` | Plot management |

## URL Construction Examples

### Seller Service Requests
- **Login:** `http://localhost:3001/api/v1/sellers/login`
- **Profile:** `http://localhost:3001/api/v1/sellers/{sellerId}`
- **Register:** `http://localhost:3001/api/v1/sellers/register`

### Farm Service Requests
- **List Farms:** `http://localhost:3005/api/v1/farms`
- **Farm Details:** `http://localhost:3005/api/v1/farms/{farmId}`
- **Create Farm:** `http://localhost:3005/api/v1/farms`

### Order Service Requests
- **List Orders:** `http://localhost:3009/api/v1/orders`
- **Order Details:** `http://localhost:3009/api/v1/orders/{orderId}`
- **Create Order:** `http://localhost:3009/api/v1/orders`

## Testing Recommendations

1. **Verify API Debug Component:** Check that all services show ✅ status
2. **Test Authentication:** Ensure login/register works correctly
3. **Test Profile Operations:** Verify profile loading and updates
4. **Test Farm Management:** Check farm creation and listing
5. **Test Order Processing:** Verify order operations
6. **Monitor Network Requests:** Use browser dev tools to verify correct URLs

## Benefits of This Fix

1. **Eliminates Double Path Issues:** No more `/sellers/sellers/login` problems
2. **Direct Microservice Access:** Bypasses API Gateway for better performance
3. **Consistent URL Construction:** All services follow the same pattern
4. **Better Error Handling:** Clearer error messages from direct service calls
5. **Improved Debugging:** Enhanced debug component for easier troubleshooting
6. **Environment Flexibility:** Easy to switch between development and production URLs

## Files Modified Summary

1. `libs/shared-utils/src/lib/environment/environment.config.ts` - Fixed service base URLs
2. `apps/seller-app/.env` - Added microservice environment variables
3. `apps/seller-app/vite.config.ts` - Configured environment variable exposure
4. `apps/seller-admin-app/vite.config.ts` - Configured environment variable exposure
5. `libs/shared-utils/src/lib/axios/instance.ts` - Updated comments and documentation
6. `apps/seller-app/src/app/components/ApiDebugInfo.tsx` - Enhanced debug component

## Next Steps

1. **Test the application** to ensure all API calls work correctly
2. **Verify the debug component** shows all services as correctly configured
3. **Monitor network requests** to confirm proper URL construction
4. **Update any remaining services** that might still have incorrect configurations
