---
description: 
globs: 
alwaysApply: true
---
For all designs I ask you to make, have them be beautiful, not cookie cutter. Make webpages that are fully featured and worthy for production.\n\nBy default, this template supports JSX syntax with Tailwind CSS classes, React hooks, and Lucide React for icons. Do not install other packages for UI themes, icons, etc unless absolutely necessary or I request them.\n\nUse icons from lucide-react for logos.\n\nUse stock photos from unsplash where appropriate, only valid URLs you know exist. Do not download the images, only link to them in image tags

<design_instructions>
  When creating designs or UIs for applications, follow these guidelines indefinitely this is non-negotiable:

  CRITICAL:
  - Always strive for professional, beautiful, and unique designs
  - All designs should be fully featured and worthy of production use
  - Never create designs with placeholder content unless explicitly requested
  - Inspired by Apple-level design polish
  - Subtle animations for scroll reveals and interactive elements
  - Subtle shadows and rounded corners for dimensional depth
  - Generous whitespace and clear visual hierarchy following 8px spacing system
  - Always create interactive and engaging designs that go beyond static visuals.
    - Each UI component must serve a functional purpose (e.g., a gallery should allow image zoom/expansion, a form should validate in real time).
    - Mimic user expectations — cards should be clickable if they represent a navigable entity, lists should be filterable/searchable, etc.
    - Prioritize micro-interactions (e.g., hover states, click animations, transitions) to give users responsive feedback.
    - Always question: "What will the user want to do with this element?"
  - DO NOT in any circumstances use Unsplash for stock photos, instead you should ALWAYS use Pexels

  AVOID GENERIC DESIGN:
  - Never use basic or default layout structures without adding custom visual polish
  - Header branding MUST NOT be simple "icon and text" combos — every header should reflect product branding with intentionality, motion, and sophistication
  - Navigation should be styled contextually with advanced interaction patterns (e.g., scroll-aware transitions, content-aware menus)
  - Ensure every screen has a visual signature — avoid layouts that could be mistaken for a free template
  - Elevate common UI patterns using motion, custom icons, branding accents, layered z-depth, or illustration
  - Add scroll effects, dynamic feedback, and hover micro-transitions to enhance visual interest
  - Always ask: "Would this design impress a senior product designer at Apple or Stripe?" If not, iterate until it would

  COLOR SCHEMES:
  - Sophisticated color palette with primary, accent, and complementary colors plus neutral tones
  - Use sufficient contrast for text/background combinations (minimum 4.5:1 ratio)
  - Limit color palette to 3-5 main colors plus neutrals
  - Consider color psychology appropriate to the application purpose

  TYPOGRAPHY:
  - Use readable font sizes (minimum 16px for body text on web)
  - Choose appropriate font pairings (often one serif + one sans-serif)
  - Establish a clear typographic hierarchy
  - Use consistent line heights and letter spacing
  - Default to system fonts or Google Fonts when no preference is stated

  LAYOUT:
  - Implement responsive designs for all screen sizes
  - Optimize for both mobile and desktop experiences
  - Follow visual hierarchy principles (size, color, contrast, repetition)
  - Ensure designs are accessible and follow WCAG guidelines
  - High-contrast text ensuring readability across all sections

  RESPONSIVE DESIGN:
  - Always create designs that work well across all device sizes
  - Use flexible grids, flexible images, and media queries
  - Test layouts at common breakpoints (mobile, tablet, desktop)
  - Consider touch targets on mobile (minimum 44x44px)
  - Ensure text remains readable at all screen sizes

  COMPONENTS:
  - Design reusable components with consistent styling
  - Create purpose-built components rather than generic ones
  - Include appropriate feedback states (hover, active, disabled)
  - Ensure accessible focus states for keyboard navigation
  - Consider animations and transitions for improved UX

  IMAGES AND ASSETS:
  - Use high-quality, relevant images that enhance the user experience
  - Optimize images for performance
  - Include appropriate alt text for accessibility
  - Maintain consistent styling across all visual elements
  - Use vector icons when possible for crisp display at all sizes

  ACCESSIBILITY:
  - Ensure sufficient color contrast
  - Include focus indicators for keyboard navigation
  - Add appropriate ARIA attributes where needed
  - Design with screen readers in mind
  - Structure content logically and hierarchically

  DARK MODE:
  - Implement dark mode when requested
  - Use appropriate contrast in both light and dark modes
  - Choose colors that work well in both modes
  - Consider reduced motion preferences

  FORMS:
  - Include clear labels for all form elements
  - Add helpful validation messages
  - Design clear error states
  - Make forms as simple as possible
  - Group related form elements logically

  UI PATTERNS:
  - Use established UI patterns that users will recognize
  - Create clear visual hierarchies to guide users
  - Design intuitive navigation systems
  - Use appropriate feedback mechanisms for user actions
  - Consider progressive disclosure for complex interfaces

  ADVANCED TECHNIQUES:
  - Consider micro-interactions to enhance the user experience
  - Use animations purposefully and sparingly
  - Incorporate skeletons/loading states for better perceived performance
  - Design for multiple user roles when applicable
  - Consider internationalization needs (text expansion, RTL support)

  RESPONSIVE FRAMEWORKS:
  - When using TailwindCSS, utilize its responsive prefixes (sm:, md:, lg:, etc.)
  - Use CSS Grid and Flexbox for layouts
  - Implement appropriate container queries when needed
  - Structure mobile-first designs that progressively enhance for larger screens
</design_instructions>