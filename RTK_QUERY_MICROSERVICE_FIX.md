# RTK Query Microservice URL Fix

## Problem Identified

The seller profile API was calling `http://localhost:3000/sellers/FARMER-001` instead of the correct microservice URL `http://localhost:3001/api/v1/sellers/FARMER-001`.

### Root Cause
The RTK Query API slice was using a single `baseUrl` (`environment.apiUrl` = `http://localhost:3000`) for all endpoints, but for direct microservice access, each service needs its own base URL.

**Before:**
```javascript
// ❌ WRONG - All endpoints used the same base URL
const baseQuery = fetchBaseQuery({
  baseUrl: environment.apiUrl, // http://localhost:3000
});

// This resulted in:
getSellerProfile: builder.query({
  query: (sellerId: string) => `/sellers/${sellerId}`, // → http://localhost:3000/sellers/FARMER-001
})
```

**After:**
```javascript
// ✅ CORRECT - Each endpoint uses its specific microservice URL
getSellerProfile: builder.query({
  query: (sellerId: string) => ({
    url: `${environment.services.sellerService}/sellers/${sellerId}`, // → http://localhost:3001/api/v1/sellers/FARMER-001
    method: 'GET',
  }),
})
```

## Solution Applied

### Fixed RTK Query Endpoints
**File Modified:** `libs/shared-utils/src/lib/redux/api/apiSlice.ts`

All endpoints were updated to use their respective microservice URLs:

#### 1. Authentication Endpoints
- **login:** `${environment.services.sellerService}/sellers/login`
- **logout:** `${environment.services.sellerService}/sellers/logout`

#### 2. Seller Profile Endpoints
- **getSellerProfile:** `${environment.services.sellerService}/sellers/${sellerId}`
- **updateSellerProfile:** `${environment.services.sellerService}/sellers/${sellerId}`
- **getSellerDashboard:** `${environment.services.sellerService}/sellers/dashboard/${sellerId}`

#### 3. Farm Endpoints
- **getFarms:** `${environment.services.farmService}/farms`
- **createFarm:** `${environment.services.farmService}/farms`
- **updateFarm:** `${environment.services.farmService}/farms/${id}`

#### 4. Crop Endpoints
- **getCrops:** `${environment.services.cropService}/crops`
- **createCrop:** `${environment.services.cropService}/crops`
- **updateCropHealth:** `${environment.services.cropService}/crops/${id}/health`

#### 5. Plot Endpoints
- **getPlots:** `${environment.services.plotService}/plots`
- **updatePlotStatus:** `${environment.services.plotService}/plots/${id}/status`

#### 6. Order Endpoints
- **getOrders:** `${environment.services.orderService}/orders`
- **createOrder:** `${environment.services.orderService}/orders`

#### 7. Notification Endpoints
- **getNotifications:** `${environment.services.notificationService}/notifications`
- **markNotificationRead:** `${environment.services.notificationService}/notifications/${id}/read`

#### 8. Analytics Endpoints
- **getFarmAnalytics:** `${environment.services.analyticsService}/analytics/farm/${farmId}`
- **getRevenueAnalytics:** `${environment.services.analyticsService}/analytics/revenue`

#### 9. Admin Endpoints
- **adminLogin:** `${environment.services.adminService}/admin/login`
- **listSellers:** `${environment.services.adminService}/admin/sellers`
- **onboardSeller:** `${environment.services.adminService}/admin/sellers`

## URL Mapping Results

### Before Fix:
- Seller Profile: `http://localhost:3000/sellers/FARMER-001` ❌
- Farm Data: `http://localhost:3000/farms` ❌
- Crop Data: `http://localhost:3000/crops` ❌

### After Fix:
- Seller Profile: `http://localhost:3001/api/v1/sellers/FARMER-001` ✅
- Farm Data: `http://localhost:3005/api/v1/farms` ✅
- Crop Data: `http://localhost:3004/api/v1/crops` ✅

## Microservice Port Mapping

| Service | Port | Base URL | RTK Query Usage |
|---------|------|----------|-----------------|
| Seller Service | 3001 | `http://localhost:3001/api/v1` | Authentication, profiles |
| Admin Service | 3002 | `http://localhost:3002/api/v1` | Admin operations |
| Analytics Service | 3003 | `http://localhost:3003/api/v1` | Analytics, reporting |
| Crop Service | 3004 | `http://localhost:3004/api/v1` | Crop management |
| Farm Service | 3005 | `http://localhost:3005/api/v1` | Farm management |
| Financial Service | 3006 | `http://localhost:3006/api/v1` | Payments, transactions |
| Inventory Service | 3007 | `http://localhost:3007/api/v1` | Stock management |
| Notification Service | 3008 | `http://localhost:3008/api/v1` | Notifications |
| Order Service | 3009 | `http://localhost:3009/api/v1` | Order processing |
| Plot Service | 3010 | `http://localhost:3010/api/v1` | Plot management |

## Testing the Fix

### 1. Restart Development Server
```bash
# Stop current server (Ctrl+C)
# Restart seller app
npm run serve:seller-app
# or
npx nx serve seller-app
```

### 2. Test Seller Profile
- Navigate to a seller profile page
- Open browser dev tools (F12) → Network tab
- The API call should now be: `http://localhost:3001/api/v1/sellers/FARMER-001`

### 3. Verify API Debug Component
- Check that the API Debug component shows all services with ✅ status
- Seller Service should show: `http://localhost:3001/api/v1`

### 4. Test Other Features
- Farm management → Should call `http://localhost:3005/api/v1/farms`
- Crop management → Should call `http://localhost:3004/api/v1/crops`
- Orders → Should call `http://localhost:3009/api/v1/orders`

## Benefits of This Fix

1. **Correct Microservice Routing:** Each API call goes to its intended microservice
2. **Direct Service Access:** Bypasses API Gateway for better performance
3. **Proper Load Distribution:** Different services handle their specific operations
4. **Better Error Handling:** Service-specific error responses
5. **Improved Debugging:** Clear service boundaries in network requests

## Files Modified

1. **`libs/shared-utils/src/lib/redux/api/apiSlice.ts`** - Updated all RTK Query endpoints to use direct microservice URLs

## Expected Results

After this fix:
- ✅ Seller profile loads from `http://localhost:3001/api/v1/sellers/FARMER-001`
- ✅ Farm data loads from `http://localhost:3005/api/v1/farms`
- ✅ All services use their correct microservice endpoints
- ✅ No more incorrect port 3000 usage for microservice calls
- ✅ Direct microservice communication without API Gateway

## Next Steps

1. **Restart the development server** to apply changes
2. **Test seller profile functionality** to confirm the fix
3. **Monitor network requests** to verify correct URLs
4. **Test other features** (farms, crops, orders) to ensure they work correctly
