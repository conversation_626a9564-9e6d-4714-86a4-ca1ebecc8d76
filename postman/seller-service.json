{"info": {"name": "AgriTech Seller Service", "description": "Seller registration, authentication, and profile management", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3001/api/v1/sellers", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}, {"key": "seller_id", "value": "", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3001/health", "protocol": "http", "host": ["localhost"], "port": "3001", "path": ["health"]}}, "response": []}, {"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.environment.set('jwt_token', response.token);", "        pm.collectionVariables.set('jwt_token', response.token);", "    }", "    if (response.seller && response.seller.id) {", "        pm.environment.set('seller_id', response.seller.id);", "        pm.collectionVariables.set('seller_id', response.seller.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"farmer123\"\n}"}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}}, "response": []}, {"name": "Seller Register", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.seller && response.seller.id) {", "        pm.environment.set('seller_id', response.seller.id);", "        pm.collectionVariables.set('seller_id', response.seller.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON> <PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"farmer123\",\n  \"phone\": \"+**********\",\n  \"location\": {\n    \"state\": \"Karnataka\",\n    \"district\": \"Bangalore\",\n    \"coordinates\": {\n      \"latitude\": 12.9716,\n      \"longitude\": 77.5946\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/register", "host": ["{{base_url}}"], "path": ["register"]}}, "response": []}]}, {"name": "Seller Management", "item": [{"name": "Get All Sellers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}?page=1&limit=10", "host": ["{{base_url}}"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "state", "value": "Karnataka", "disabled": true}, {"key": "verified", "value": "true", "disabled": true}]}}, "response": []}, {"name": "<PERSON> Seller Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/profile", "host": ["{{base_url}}"], "path": ["profile"]}}, "response": []}, {"name": "Update Seller Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Farmer Name\",\n  \"phone\": \"+**********\",\n  \"location\": {\n    \"state\": \"Karnataka\",\n    \"district\": \"Mysore\",\n    \"coordinates\": {\n      \"latitude\": 12.2958,\n      \"longitude\": 76.6394\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/profile", "host": ["{{base_url}}"], "path": ["profile"]}}, "response": []}, {"name": "Delete Seller Account", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/profile", "host": ["{{base_url}}"], "path": ["profile"]}}, "response": []}]}, {"name": "KYC & Documents", "item": [{"name": "Upload KYC Document", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "document", "type": "file", "src": []}, {"key": "type", "value": "kyc", "type": "text"}, {"key": "description", "value": "<PERSON><PERSON><PERSON><PERSON>", "type": "text"}]}, "url": {"raw": "{{base_url}}/{{seller_id}}/documents", "host": ["{{base_url}}"], "path": ["{{seller_id}}", "documents"]}}, "response": []}, {"name": "Get Seller Documents", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/{{seller_id}}/documents", "host": ["{{base_url}}"], "path": ["{{seller_id}}", "documents"]}}, "response": []}, {"name": "Submit KYC Documents", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"documents\": [\n    {\n      \"documentType\": \"aadhaar\",\n      \"documentNumber\": \"1234-5678-9012\",\n      \"documentUrl\": \"https://example.com/aadhaar.pdf\"\n    },\n    {\n      \"documentType\": \"address_proof\",\n      \"documentNumber\": \"ADDRESS-001\",\n      \"documentUrl\": \"https://example.com/address.pdf\"\n    }\n  ],\n  \"personalDetails\": {\n    \"fullName\": \"John <PERSON>\",\n    \"dateOfBirth\": \"1990-01-01\",\n    \"fatherName\": \"Father Name\",\n    \"aadharNumber\": \"1234-5678-9012\"\n  }\n}"}, "url": {"raw": "{{base_url}}/kyc/submit", "host": ["{{base_url}}"], "path": ["kyc", "submit"]}}, "response": []}, {"name": "Get KYC Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/kyc/status", "host": ["{{base_url}}"], "path": ["kyc", "status"]}}, "response": []}, {"name": "Get Seller Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/dashboard", "host": ["{{base_url}}"], "path": ["dashboard"]}}, "response": []}, {"name": "Update Bank Details", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"accountNumber\": \"**********\",\n  \"bankName\": \"HDFC Bank\",\n  \"ifscCode\": \"HDFC0001234\",\n  \"accountHolderName\": \"<PERSON>\",\n  \"branchName\": \"Main Branch\"\n}"}, "url": {"raw": "{{base_url}}/profile/bank-details", "host": ["{{base_url}}"], "path": ["profile", "bank-details"]}}, "response": []}]}, {"name": "Farm Management", "item": [{"name": "Get Seller Farms", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/farms", "host": ["{{base_url}}"], "path": ["farms"]}}, "response": []}, {"name": "Create Farm", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Green Valley Farm\",\n  \"location\": {\n    \"state\": \"Karnataka\",\n    \"district\": \"Bangalore\",\n    \"village\": \"Whitefield\",\n    \"coordinates\": {\n      \"latitude\": 12.9698,\n      \"longitude\": 77.7500\n    }\n  },\n  \"size\": 5.5,\n  \"soilType\": \"LOAMY\",\n  \"waterSource\": \"BOREWELL\",\n  \"organicCertified\": true\n}"}, "url": {"raw": "{{base_url}}/farms", "host": ["{{base_url}}"], "path": ["farms"]}}, "response": []}]}, {"name": "Search & Analytics", "item": [{"name": "Search Sellers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/search?q=organic&location=12.9716,77.5946&radius=50", "host": ["{{base_url}}"], "path": ["search"], "query": [{"key": "q", "value": "organic"}, {"key": "location", "value": "12.9716,77.5946"}, {"key": "radius", "value": "50"}, {"key": "state", "value": "Karnataka", "disabled": true}, {"key": "verified", "value": "true", "disabled": true}]}}, "response": []}, {"name": "<PERSON> Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/{{seller_id}}/analytics", "host": ["{{base_url}}"], "path": ["{{seller_id}}", "analytics"]}}, "response": []}]}, {"name": "Profile & Settings", "item": [{"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/profile", "host": ["{{base_url}}"], "path": ["profile"]}}, "response": []}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Profile Name\",\n  \"phone\": \"+**********\",\n  \"preferences\": {\n    \"language\": \"en\",\n    \"notifications\": {\n      \"email\": true,\n      \"sms\": false,\n      \"push\": true\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/profile", "host": ["{{base_url}}"], "path": ["profile"]}}, "response": []}, {"name": "Change Password", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"farmer123\",\n  \"newPassword\": \"newpassword123\",\n  \"confirmPassword\": \"newpassword123\"\n}"}, "url": {"raw": "{{base_url}}/change-password", "host": ["{{base_url}}"], "path": ["change-password"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set timestamp for requests", "pm.globals.set('timestamp', new Date().toISOString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test for response time", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// Global test for response format", "pm.test('Response has valid JSON format', function () {", "    pm.response.to.have.jsonBody();", "});"]}}]}