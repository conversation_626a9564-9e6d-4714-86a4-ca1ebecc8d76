{"info": {"name": "AgriTech Farm Service - Complete API Collection", "description": "Comprehensive farm management APIs including Farm Service, Seller Service Farm Management, and Admin Service Farm Operations", "version": "2.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "farm_service_url", "value": "http://localhost:3005/api/farms", "type": "string"}, {"key": "seller_service_url", "value": "http://localhost:3001/api/v1/sellers", "type": "string"}, {"key": "admin_service_url", "value": "http://localhost:3002/api/v1/admin", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}, {"key": "farm_id", "value": "", "type": "string"}, {"key": "seller_id", "value": "", "type": "string"}, {"key": "crop_id", "value": "", "type": "string"}], "item": [{"name": "Health Checks", "item": [{"name": "Farm Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3005/health", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["health"]}}, "response": []}, {"name": "Seller Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3001/health", "protocol": "http", "host": ["localhost"], "port": "3001", "path": ["health"]}}, "response": []}, {"name": "Admin Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3002/health", "protocol": "http", "host": ["localhost"], "port": "3002", "path": ["health"]}}, "response": []}]}, {"name": "Farm Service - Direct Farm Management", "item": [{"name": "Query All Farms", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{farm_service_url}}?page=1&limit=10&sellerId={{seller_id}}&status=ACTIVE&soilType=LOAMY&waterSource=BOREWELL&state=Karnataka&city=Bangalore&sortBy=createdAt&sortOrder=desc", "host": ["{{farm_service_url}}"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "sellerId", "value": "{{seller_id}}", "disabled": true}, {"key": "status", "value": "ACTIVE", "disabled": true}, {"key": "soilType", "value": "LOAMY", "disabled": true}, {"key": "waterSource", "value": "BOREWELL", "disabled": true}, {"key": "state", "value": "Karnataka", "disabled": true}, {"key": "city", "value": "Bangalore", "disabled": true}, {"key": "sortBy", "value": "createdAt", "disabled": true}, {"key": "sortOrder", "value": "desc", "disabled": true}]}}, "response": []}, {"name": "Get Farm by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{farm_service_url}}/{{farm_id}}", "host": ["{{farm_service_url}}"], "path": ["{{farm_id}}"]}}, "response": []}, {"name": "Create Farm", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.success && response.data && response.data.farmId) {", "        pm.environment.set('farm_id', response.data.farmId);", "        pm.collectionVariables.set('farm_id', response.data.farmId);", "        console.log('Farm ID set to:', response.data.farmId);", "    }", "}", "", "pm.test('Farm created successfully', function () {", "    pm.expect(pm.response.code).to.equal(201);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('farmId');", "    pm.expect(response.data).to.have.property('name');", "    pm.expect(response.data).to.have.property('location');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"sellerId\": \"{{seller_id}}\",\n  \"name\": \"Green Valley Organic Farm\",\n  \"location\": {\n    \"country\": \"INDIA\",\n    \"state\": \"Karnataka\",\n    \"city\": \"Bangalore\",\n    \"pincode\": \"562110\",\n    \"addressLine1\": \"Plot 123, Green Valley\",\n    \"addressLine2\": \"Devanahalli Village\",\n    \"coordinates\": {\n      \"latitude\": 13.2543,\n      \"longitude\": 77.7022\n    }\n  },\n  \"totalArea\": 12.5,\n  \"soilType\": \"LOAMY\",\n  \"waterSource\": \"BOREWELL\",\n  \"infrastructure\": [\n    \"STORAGE_WAREHOUSE\",\n    \"IRRIGATION_SYSTEM\",\n    \"PROCESSING_UNIT\"\n  ],\n  \"certifications\": [\n    \"ORGANIC_CERTIFIED\",\n    \"ISO_9001\"\n  ]\n}"}, "url": {"raw": "{{farm_service_url}}", "host": ["{{farm_service_url}}"]}}, "response": []}, {"name": "Update Farm", "event": [{"listen": "test", "script": {"exec": ["pm.test('Farm updated successfully', function () {", "    pm.expect(pm.response.code).to.equal(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('farmId');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Green Valley Organic Farm\",\n  \"totalArea\": 15.0,\n  \"soilType\": \"CLAY_LOAM\",\n  \"waterSource\": \"CANAL\",\n  \"infrastructure\": [\n    \"STORAGE_WAREHOUSE\",\n    \"PROCESSING_UNIT\",\n    \"COLD_STORAGE\",\n    \"SOLAR_PANELS\"\n  ],\n  \"certifications\": [\n    \"ORGANIC_CERTIFIED\",\n    \"ISO_9001\",\n    \"FAIR_TRADE\"\n  ],\n  \"status\": \"ACTIVE\"\n}"}, "url": {"raw": "{{farm_service_url}}/{{farm_id}}", "host": ["{{farm_service_url}}"], "path": ["{{farm_id}}"]}}, "response": []}, {"name": "Delete Farm", "event": [{"listen": "test", "script": {"exec": ["pm.test('Farm deleted successfully', function () {", "    pm.expect(pm.response.code).to.equal(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{farm_service_url}}/{{farm_id}}", "host": ["{{farm_service_url}}"], "path": ["{{farm_id}}"]}}, "response": []}]}, {"name": "Farm Service - Search & Discovery", "item": [{"name": "Search Farms (Elasticsearch)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Search farms successful', function () {", "    pm.expect(pm.response.code).to.equal(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{farm_service_url}}/search?q=organic&location=13.2543,77.7022&radius=50&page=1&limit=10&soilType=LOAMY&minSize=5&maxSize=20", "host": ["{{farm_service_url}}"], "path": ["search"], "query": [{"key": "q", "value": "organic"}, {"key": "location", "value": "13.2543,77.7022"}, {"key": "radius", "value": "50"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "soilType", "value": "LOAMY", "disabled": true}, {"key": "minSize", "value": "5", "disabled": true}, {"key": "maxSize", "value": "20", "disabled": true}]}}, "response": []}, {"name": "Get Farm Analytics by <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test('Farm analytics retrieved successfully', function () {", "    pm.expect(pm.response.code).to.equal(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{farm_service_url}}/analytics/{{seller_id}}", "host": ["{{farm_service_url}}"], "path": ["analytics", "{{seller_id}}"]}}, "response": []}, {"name": "Reindex All Farms", "event": [{"listen": "test", "script": {"exec": ["pm.test('Farms reindexed successfully', function () {", "    pm.expect(pm.response.code).to.equal(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{farm_service_url}}/reindex", "host": ["{{farm_service_url}}"], "path": ["reindex"]}}, "response": []}]}, {"name": "Seller Service - Farm Management", "item": [{"name": "Get Seller Farms", "event": [{"listen": "test", "script": {"exec": ["pm.test('Seller farms retrieved successfully', function () {", "    pm.expect(pm.response.code).to.equal(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{seller_service_url}}/farms", "host": ["{{seller_service_url}}"], "path": ["farms"]}}, "response": []}, {"name": "Create Farm via Seller Service", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.success && response.data && response.data.farmId) {", "        pm.environment.set('farm_id', response.data.farmId);", "        pm.collectionVariables.set('farm_id', response.data.farmId);", "        console.log('Farm ID set to:', response.data.farmId);", "    }", "}", "", "pm.test('Farm created via seller service', function () {", "    pm.expect(pm.response.code).to.equal(201);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('farmId');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON><PERSON>'s Premium Farm\",\n  \"location\": {\n    \"country\": \"INDIA\",\n    \"state\": \"Karnataka\",\n    \"city\": \"Mysore\",\n    \"pincode\": \"570001\",\n    \"addressLine1\": \"Farm Road 456\",\n    \"addressLine2\": \"Near Mysore Palace\",\n    \"coordinates\": {\n      \"latitude\": 12.2958,\n      \"longitude\": 76.6394\n    }\n  },\n  \"totalArea\": 8.5,\n  \"soilType\": \"RED_SOIL\",\n  \"waterSource\": \"RIVER\",\n  \"infrastructure\": [\n    \"DRIP_IRRIGATION\",\n    \"GREENHOUSE\",\n    \"STORAGE_FACILITY\"\n  ],\n  \"certifications\": [\n    \"ORGANIC_CERTIFIED\"\n  ],\n  \"farmingPractices\": {\n    \"primaryMethod\": \"ORGANIC\",\n    \"irrigationSystems\": [\n      \"DRIP_IRRIGATION\",\n      \"SPRINKLER_SYSTEM\"\n    ],\n    \"sustainabilityScore\": 85\n  }\n}"}, "url": {"raw": "{{seller_service_url}}/farms", "host": ["{{seller_service_url}}"], "path": ["farms"]}}, "response": []}, {"name": "Get Farm by ID via Seller Service", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{seller_service_url}}/farms/{{farm_id}}", "host": ["{{seller_service_url}}"], "path": ["farms", "{{farm_id}}"]}}, "response": []}, {"name": "Update Farm via Seller Service", "event": [{"listen": "test", "script": {"exec": ["pm.test('Farm updated via seller service', function () {", "    pm.expect(pm.response.code).to.equal(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Premium Farm\",\n  \"totalArea\": 10.0,\n  \"soilType\": \"BLACK_SOIL\",\n  \"waterSource\": \"BOREWELL\",\n  \"infrastructure\": [\n    \"DRIP_IRRIGATION\",\n    \"GREENHOUSE\",\n    \"STORAGE_FACILITY\",\n    \"SOLAR_POWER\"\n  ],\n  \"farmingPractices\": {\n    \"primaryMethod\": \"INTEGRATED\",\n    \"irrigationSystems\": [\n      \"DRIP_IRRIGATION\",\n      \"SPRINKLER_SYSTEM\",\n      \"MICRO_IRRIGATION\"\n    ],\n    \"sustainabilityScore\": 90\n  }\n}"}, "url": {"raw": "{{seller_service_url}}/farms/{{farm_id}}", "host": ["{{seller_service_url}}"], "path": ["farms", "{{farm_id}}"]}}, "response": []}, {"name": "Update Crop Rotation Plan", "event": [{"listen": "test", "script": {"exec": ["pm.test('Crop rotation updated successfully', function () {", "    pm.expect(pm.response.code).to.equal(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"cropRotationPlan\": [\n    {\n      \"plotId\": \"plot-001\",\n      \"season\": \"KHARIF\",\n      \"year\": 2024,\n      \"plannedCrop\": \"Rice\",\n      \"variety\": \"Basmati\",\n      \"status\": \"PLANNED\"\n    },\n    {\n      \"plotId\": \"plot-001\",\n      \"season\": \"RABI\",\n      \"year\": 2024,\n      \"plannedCrop\": \"Wheat\",\n      \"variety\": \"Durum\",\n      \"status\": \"PLANNED\"\n    },\n    {\n      \"plotId\": \"plot-002\",\n      \"season\": \"KHARIF\",\n      \"year\": 2024,\n      \"plannedCrop\": \"Sugarcane\",\n      \"status\": \"PLANTED\"\n    }\n  ]\n}"}, "url": {"raw": "{{seller_service_url}}/farms/{{farm_id}}/rotation", "host": ["{{seller_service_url}}"], "path": ["farms", "{{farm_id}}", "rotation"]}}, "response": []}, {"name": "Get Farm Analytics via Seller Service", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{seller_service_url}}/farms/{{farm_id}}/analytics", "host": ["{{seller_service_url}}"], "path": ["farms", "{{farm_id}}", "analytics"]}}, "response": []}, {"name": "Search Farms via Seller Service", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{seller_service_url}}/farms/search?q=organic&location=12.9716,77.5946&radius=50", "host": ["{{seller_service_url}}"], "path": ["farms", "search"], "query": [{"key": "q", "value": "organic"}, {"key": "location", "value": "12.9716,77.5946"}, {"key": "radius", "value": "50"}]}}, "response": []}, {"name": "Delete Farm via Seller Service", "event": [{"listen": "test", "script": {"exec": ["pm.test('Farm deleted via seller service', function () {", "    pm.expect(pm.response.code).to.equal(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{seller_service_url}}/farms/{{farm_id}}", "host": ["{{seller_service_url}}"], "path": ["farms", "{{farm_id}}"]}}, "response": []}]}, {"name": "Admin Service - Farm Management", "item": [{"name": "Get All Farms (Admin)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Admin farms retrieved successfully', function () {", "    pm.expect(pm.response.code).to.equal(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{admin_service_url}}/farms?page=1&limit=10&sellerId={{seller_id}}&status=ACTIVE", "host": ["{{admin_service_url}}"], "path": ["farms"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "sellerId", "value": "{{seller_id}}", "disabled": true}, {"key": "status", "value": "ACTIVE", "disabled": true}]}}, "response": []}, {"name": "Get Farm by ID (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{admin_service_url}}/farms/{{farm_id}}", "host": ["{{admin_service_url}}"], "path": ["farms", "{{farm_id}}"]}}, "response": []}, {"name": "Verify Farm (Admin)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Farm verified successfully by admin', function () {", "    pm.expect(pm.response.code).to.equal(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"verified\": true,\n  \"verificationNotes\": \"Farm location, infrastructure, and organic certification verified. All documents are in order.\",\n  \"verifiedBy\": \"admin-001\",\n  \"verificationDate\": \"{{$isoTimestamp}}\",\n  \"certificationLevel\": \"PREMIUM_ORGANIC\"\n}"}, "url": {"raw": "{{admin_service_url}}/farms/{{farm_id}}/verify", "host": ["{{admin_service_url}}"], "path": ["farms", "{{farm_id}}", "verify"]}}, "response": []}]}, {"name": "Data Management & Utilities", "item": [{"name": "Seed Farm Data", "event": [{"listen": "test", "script": {"exec": ["pm.test('Farm data seeded successfully', function () {", "    pm.expect(pm.response.code).to.equal(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{farm_service_url}}/seed", "host": ["{{farm_service_url}}"], "path": ["seed"]}}, "response": []}, {"name": "Get Farm Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{farm_service_url}}/statistics?groupBy=state&status=ACTIVE", "host": ["{{farm_service_url}}"], "path": ["statistics"], "query": [{"key": "groupBy", "value": "state"}, {"key": "status", "value": "ACTIVE", "disabled": true}]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set farm-specific timestamp and common variables", "pm.globals.set('farm_timestamp', new Date().toISOString());", "", "// Auto-generate test data if needed", "if (!pm.collectionVariables.get('seller_id')) {", "    pm.collectionVariables.set('seller_id', 'seller-' + Math.random().toString(36).substr(2, 9));", "}", "", "// Set default headers for all requests", "pm.request.headers.add({", "    key: 'Content-Type',", "    value: 'application/json'", "});"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global farm service tests", "pm.test('Farm API response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// Test for successful responses", "if (pm.response.code >= 200 && pm.response.code < 300) {", "    pm.test('Response has valid JSON structure', function () {", "        const response = pm.response.json();", "        pm.expect(response).to.be.an('object');", "        pm.expect(response).to.have.property('success');", "    });", "}", "", "// Test for error responses", "if (pm.response.code >= 400) {", "    pm.test('Error response has proper structure', function () {", "        const response = pm.response.json();", "        pm.expect(response).to.be.an('object');", "        pm.expect(response).to.have.property('success');", "        pm.expect(response.success).to.be.false;", "        pm.expect(response).to.have.property('message');", "    });", "}", "", "// Log response for debugging", "console.log('Response Status:', pm.response.code);", "console.log('Response Time:', pm.response.responseTime + 'ms');"]}}]}